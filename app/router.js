module.exports = app => {
  const { router, controller, config } = app;
  const branch = config.branch && config.branch !== 'master' ? config.branch : ''; // 分支
  const isOrgSelfRegisterEnabled = config.isOrgSelfRegisterEnabled || '0';
  const allowLogin = config.allowLogin;
  // 全局路由过滤器
  router.all('*', async (ctx, next) => {
    if (ctx.request.url.includes('undefined')) {
      ctx.status = 400;
      ctx.body = {
        error: 'Invalid request path',
      };
      return;
    }
    await next();
  });
  router.get('/', [ 'hz', 'fz', 'fj', 'demo', 'xhl', 'ldq', 'by', 'sxcc', 'wh' ].includes(branch) ? controller.home[branch + 'Enter'] : controller.home.getHomePage);
  router.get('/byCallback', controller.home.byCallback);
  router.get('/whcallback', controller.home.whCallback);
  router.get(`${config.admin_base_path}`, controller.home.getHomePage);
  router.get('/index', controller.home.getIndexPage);
  router.get('/survey/:id', controller.home.getSurveyPage);
  router.get('/enterprise/orgapply', controller.home.getApplyCorp);
  router.post('/enterprise/aliSMSApi', controller.home.aliSMSApi);
  // router.post('/enterprise/getCompanyByCode', controller.api.systemConfig.getCompanyByCode);
  router.get('/manage/getAssessmentInfo', controller.home.getAssessmentInfo);

  router.get('/services', controller.home.getServicesPage);
  router.get('/regulation', controller.home.getRegulationPage);
  router.get('/consult', controller.home.getConsultPage);
  router.get('/detail', controller.home.getDetailPage);
  router.get('/contentList', controller.home.getContentListPage);


  // 检索代号：#0001 后台管理根目录 例如：config.user_base_path
  router.get(`${config.admin_base_path}/api`, controller.home.adminApi);

  router.get(`${config.qy_base_path}`, controller.home.admin);
  router.get(`${config.qy_base_path}/api`, controller.home.adminApi);

  router.get(`${config.user_base_path}`, controller.home.admin);
  router.get(`${config.user_base_path}/api`, controller.home.adminApi);
  // login pages
  router.get(`${config.admin_base_path}/login`, allowLogin ? controller.home.getLoginPage : controller.home[branch + 'Enter']);
  // admin/dataBigScreen
  router.get(`${config.admin_base_path}/dataBigScreenAuth`, controller.manage.adminUser.dataBigScreenAuth);
  // register pages
  isOrgSelfRegisterEnabled === '1' && router.get(`${config.admin_base_path}/reg`, controller.home.getRegPage);
  router.get('/trial', controller.home.getTryPage);

  // 仓库管理路由
  router.get('/manage/warehouse/list', controller.manage.warehouse.getWarehouseList);
  router.post('/manage/warehouse/create', controller.manage.warehouse.createWarehouse);
  router.post('/manage/warehouse/update', controller.manage.warehouse.updateWarehouse);
  router.post('/manage/warehouse/delete', controller.manage.warehouse.deleteWarehouse);
  router.get('/manage/warehouse/companyList', controller.manage.warehouse.getCompanyList);
  router.get('/manage/warehouse/workplaces', controller.manage.warehouse.getWorkplacesByCompany);
  router.get('/manage/warehouse/millConstructionTree', controller.manage.warehouse.getMillConstructionTree);
  router.get('/manage/warehouse/managedFullIds', controller.manage.warehouse.getManagedFullIds);
  router.post('/manage/warehouse/initPublicWarehouse', controller.manage.warehouse.initPublicWarehouse);

  router.get('/service', controller.home.getServicePage);
  router.get('/official', controller.home.getOfficialPage);
  router.get('/help', controller.home.getHelpPage);
  router.get('/connect', controller.home.getConnectPage);
  router.get('/home', controller.home.getHomePage);
  router.get('/api/home/<USER>', controller.home.articleDetail);

  // 管理员退出
  router.get('/manage/logout', controller.home.logOutAction);
  router.get('/manage/returnLogin', controller.home.returnLogin);
  router.get('/manage/checkUserName', controller.home.checkUserName);
  router.post('/api/admin/findInFirms', controller.home.findInFirms);
  router.get('/manage/getPersonInfo', controller.home.getPersonInfo);
  // router.post('/api/admin/doLogin', controller.manage.home.getLoginPage);
  router.post('/api/admin/fillArea', controller.home.fullArea);
  router.post('/api/admin/doLogin', controller.home.loginAction);
  router.post('/api/admin/SmdoLogin', controller.home.smloginAction);
  router.post('/api/admin/codeLogin', controller.home.codeLoginAction);
  router.post('/api/user/sendVerificationCode', controller.home.sendVerificationCode);
  router.post('/api/user/doReg', controller.home.regAction);
  router.post('/api/user/doRegTry', controller.home.regTryAction);
  router.post('/api/user/checkRegTry', controller.home.checkRegTry);
  router.post('/api/enterprise/upload/images', controller.api.systemConfig.uploadEnterpriseImage);
  router.post('/api/enterprise/upload/file', controller.api.systemConfig.uploadEnterpriseFile);
  router.post('/api/enterprise/upload/IdCard1', controller.api.systemConfig.uploadFrontIDCardImage);
  router.post('/api/enterprise/upload/IdCard2', controller.api.systemConfig.uploadBackIDCardImage);

  router.get('/api/address/list', controller.api.systemConfig.addressList);
  router.get('/api/user/getUserInfo', controller.home.getUserInfoBySession);
  router.post('/api/user/updateInfo', controller.home.updateUserInfo);
  router.post('/api/user/checkPhoneCode', controller.home.checkPhoneCode);
  router.get('/api/user/getSubCompanies', controller.home.getSubCompanies);
  router.post('/api/user/switchCompanies', controller.home.switchCompanies);
  // router.get('api/user/getAllPhoneNum',controller.home.getAllPhoneNum)
  // 上传资质照片
  router.post('/api/upload/qualifiesImages', controller.api.systemConfig.uploadQualifiesImages);
  // 根据资质idArr获取先关资质详情
  router.post('/api/qualifies/findByIdArr', controller.api.orgQualifies.findByIdArr);
  // 创建新的机构资质
  router.post('/api/qualifies/create', controller.api.orgQualifies.create);
  // 通过id查询机构资质
  router.get('/api/qualifies', controller.api.orgQualifies.getById);
  // 修改机构资质信息
  router.post('/api/qualifies/update', controller.api.orgQualifies.update);
  // 删除资质
  router.get('/api/qualifies/delete', controller.api.orgQualifies.delete); // 参数：_id

  // 必备路由，慎重修改 ===================================================

  // 后台管理界面  qy  user
  router.get([ `${config.admin_base_path}/:page`, `${config.admin_base_path}/:page/:page1`, `${config.admin_base_path}/:page/:page1/:id`, `${config.admin_base_path}/:page/:page1/:id/:status` ], controller.manage.adminUser.dashboard);

  router.get([ `${config.qy_base_path}/:page`, `${config.qy_base_path}/:page/:page1`, `${config.qy_base_path}/:page/:page1/:id`, `${config.qy_base_path}/:page/:page1/:id/:status` ], controller.manage.adminUser.dashboard);

  router.post([ `${config.user_base_path}/:page`, `${config.user_base_path}/:page/:page1`, `${config.user_base_path}/:page/:page1/:id`, `${config.user_base_path}/:page/:page1/:id/:status` ], controller.manage.adminUser.dashboard);

  // 获取管理员信息
  router.get('/manage/getUserSession', controller.manage.adminUser.getUserSession);

  // 获取后台基础信息
  router.get('/manage/getSitBasicInfo', controller.manage.adminUser.getBasicSiteInfo);

  // 获取待办事项的数据
  router.get('/manage/getBasicMessage', controller.manage.adminUser.getBasicMessage);

  // 获取每一年的体检数据getEveryYearHealthCheck
  router.get('/manage/getEveryYearHealthCheck', controller.manage.adminUser.getEveryYearHealthCheck);
  // 获取所有的接害人
  router.get('/manage/harmStatisticsPerson', controller.manage.adminUser.harmStatisticsPerson);
  // 获取企业档案完成情况
  router.get('/manage/getFilesCompleteness', controller.manage.adminUser.getFilesCompleteness);
  // 获取员工性别比例，职业卫生负责人等信息
  router.get('/manage/getMainData', controller.manage.adminUser.getMainData);
  router.get('/manage/getCheckStatistics', controller.manage.adminUser.getCheckStatistics);
  router.get('/manage/jcTend', controller.manage.adminUser.jcTend);
  router.get('/manage/whjcData', controller.manage.adminUser.whjcData);
  router.get('/manage/getEducationData', controller.manage.adminUser.getEducationData);
  router.get('/manage/tjEcharts', controller.manage.adminUser.tjEcharts);
  router.get('/manage/tjConclusion', controller.manage.adminUser.tjConclusion);
  router.get('/manage/PPElist', controller.manage.adminUser.PPElist);
  // 初始化或更新统计表
  router.get('/manage/setStatistical', controller.manage.adminUser.setStatistical);
  // 更新统计表
  router.post('/manage/updateStatistical', controller.manage.adminUser.updateStatistical);

  // 更新全部企业职业病人数到 统计表
  router.get('/manage/updateOdiseases', controller.manage.adminUser.updateOdiseases);
  // 处理 警示标识 历史数据
  router.get('/manage/handleWarnNotice', controller.manage.adminUser.handleWarnNotice);

  // 更新某家职业病人数到 统计表
  router.get('/manage/updateOdisease', controller.manage.adminUser.updateOdisease);

  // 得到图表数据
  router.post('/manage/getChartData', controller.manage.adminUser.getChartData);

  // 获取全部消息
  router.get('/manage/getAllReadMessage', controller.manage.adminUser.getAllReadMessage);
  // 获取未读消息
  router.get('/manage/getNoReadMessage', controller.manage.adminUser.getNoReadMessage);
  // 获取已读消息
  router.get('/manage/getIsReadMessage', controller.manage.adminUser.getIsReadMessage);
  // 得到  职业健康检查异常结果  分类别数据
  router.get('/manage/getSuspectData', controller.manage.adminUser.getSuspectData);
  // 汪 的数据
  router.get('/manage/updateHealthList', controller.manage.adminUser.updateHealthList);
  router.post('/manage/getHealthCheckByYear', controller.manage.adminUser.getHealthCheckByYear);
  // =====================================================================

  // 实验室api
  router.get('/manage/oneImport', controller.manage.adminUser.updateHealthList);

  // TODO: role处理数据
  router.get('/mange/handleRole', controller.manage.adminUser.handleRole);
  // 获取企业危险点/法律风险
  router.get('/manage/dangerousPoints', controller.manage.adminUser.dangerousPoints);
  // 企业绑定当前新添加企业ID 是企业添加多个企业时用于告知是添加的哪个企业，以此获取其审核状态
  router.get('/manage/bindEnterpriseID', controller.manage.adminUser.bindEnterpriseID);

  // 临时权限所需==============================================
  /**
* 角色管理
*/
  router.get('/manage/adminGroup/getList', controller.manage.adminGroup.list);

  router.get('/manage/adminGroup/getOne', controller.manage.adminGroup.getOne);

  router.post('/manage/adminGroup/addOne', controller.manage.adminGroup.create);

  router.post('/manage/adminGroup/updateOne', controller.manage.adminGroup.update);

  router.get('/manage/adminGroup/deleteGroup', controller.manage.adminGroup.removes);

  // 鉴权用，勿删
  router.get('/manage/adminResource/getListByPower', controller.manage.adminResource.listByPower);
  /**
     * 资源管理
     *
     */

  router.get('/manage/adminResource/getList', controller.manage.adminResource.list);
  router.get('/manage/apiResource/getList', controller.manage.apiResource.list);

  // 测试
  // router.get('/manage/adminResource/alllist', controller.manage.adminResource.alllist);

  // ==========================================================================
  // TODO 后端数据处理的实时进度
  app.io.route('openProgress', app.io.controller.progress.employees);
  app.io.route('addSomeEmployee', app.io.controller.progress.addSomeEmployee);

  // htt======
  app.io.route('addMills', app.io.controller.progress.addMills);
  app.io.route('addCheckAssessments', app.io.controller.progress.addCheckAssessments);
  app.io.route('downloadAllRecords', app.io.controller.progress.downloadAllRecords);

  // xxn add 获取在线监测的设备最新数据
  app.io.route('onlineMonitoring', app.io.controller.progress.onlineMonitoring);
  app.io.route('closeOnlineMonitoring', app.io.controller.progress.closeOnlineMonitoring);


  // ==========================================================================
  // 自查报告路径

  router.post('/manage/createAssessmentReport', controller.home.createAssessmentReport);

  router.post('/manage/deleteAssessmentReport', controller.home.deleteAssessmentReport);

  router.post('/manage/updateAssessmentReport', controller.home.updateAssessmentReport);

  // 钉钉事件订阅
  router.post('/', controller.home.dingSubscript);
  // 判断数据库连接断开问题
  router.get('/api/heartbeat', controller.api.systemConfig.adoConnection);

  // 一次性获取级联地址
  router.get('/manage/superUser/getDistrict', controller.manage.superUser.getAdministrativeDivision);
  // 标记已读消息
  router.post('/manage/isReadMessage', controller.manage.adminUser.isReadMessage);
  router.post('/api/mySystemMessage', controller.home.mySystemMessage);
  // 杭州的登录页 xxn add
  router.get('/warning', controller.home.hzLoginPage);
  // 获取验证码
  router.get('/api/getImgCode', controller.home.getImgCode);
  router.get('/api/harmFactorsMap/getWhGISbaseLayerUrl', controller.home.getWhGISbaseLayerUrl);
  router.get('/api/systemConfig/getConfig', controller.api.systemConfig.list);
  router.get('/api/getAuthCookieName', controller.api.systemConfig.getAuthCookieName);
  // 空请求
  router.get('/manage/emptyRequest', controller.api.systemConfig.emptyRequest);

  /**
   * 发送消息的api
   */
  router.post('/manage/sendMessage/send', controller.manage.messageNotification.addNew);
  router.post('/manage/sendMessage/getList', controller.manage.messageNotification.findMessageList); // 通知记录
  router.get('/manage/sendMessage/getMsgOne', controller.manage.messageNotification.getMsgOne); // 通知记录
  router.get('/manage/getGroupID', controller.manage.messageNotification.getAllGroup);
  router.post('/manage/sendMessage/changeState', controller.manage.messageNotification.changeState); // 删除通知
  router.post('/manage/sendMessage/sendSms', controller.manage.messageNotification.sendSms); // 短信提醒
  router.post('/api/sendMessage/uploadFiles', controller.manage.messageNotification.uploadFiles); // 上传通知文件

  // 问卷调查
  router.post('/manage/questionnaire/addNewQuestionnaire', controller.manage.questionnaire.addNewQuestionnaire);
  router.post('/manage/questionnaire/getQuestionnaireList', controller.manage.questionnaire.getQuestionnaireList);
  router.post('/manage/questionnaire/editPublishStatus', controller.manage.questionnaire.editPublishStatus);
  router.post('/manage/questionnaire/delQuestionnaire', controller.manage.questionnaire.delQuestionnaire);
  router.post('/manage/questionnaire/getQuestionnaireDetailById', controller.manage.questionnaire.getQuestionnaireDetailById);
  router.post('/manage/questionnaire/saveQuestionnaireDetail', controller.manage.questionnaire.saveQuestionnaireDetail);
  router.post('/manage/questionnaire/setQuestionnaireTemplate', controller.manage.questionnaire.setQuestionnaireTemplate);
  router.post('/manage/questionnaire/copyNewQuestionnaire', controller.manage.questionnaire.copyNewQuestionnaire);
  router.post('/manage/questionnaire/findGroupEnterprises', controller.manage.questionnaire.findGroupEnterprises);
  router.post('/manage/template/getQuestionnaireTemplateList', controller.manage.questionnaire.getQuestionnaireTemplateList);
  router.post('/manage/template/addNewQuestionnaireTmp', controller.manage.questionnaire.addNewQuestionnaireTmp);
  router.post('/manage/template/getQuestionnaireTmpById', controller.manage.questionnaire.getQuestionnaireTmpById);
  router.post('/manage/template/delQuestionnaireTmp', controller.manage.questionnaire.delQuestionnaireTmp);
  router.post('/manage/template/saveQuestionnaireTmpDetail', controller.manage.questionnaire.saveQuestionnaireTmpDetail);
  router.post('/manage/answer/submitAnswer', controller.manage.questionnaire.submitAnswer);
  router.get('/manage/questionnaire/getQrCodeImg', controller.manage.questionnaire.getQrCodeImg);
  router.post('/manage/questionnaire/getStatistics', controller.manage.questionnaire.getStatistics);
  router.post('/manage/questionnaire/getStatisticsDetail', controller.manage.questionnaire.getStatisticsDetail);
  router.post('/manage/questionnaire/saveTempQuestionnaire', controller.manage.questionnaire.saveTempQuestionnaire);

  // 卷王单点
  router.get('/manage/checkWjUser', controller.home.checkWjUser);
  // 判断当前系统是否可登录
  router.get('/api/getAuthLogin', controller.api.systemConfig.getAuthLogin);
  // 标准库
  router.post('/manage/standardLibrary/uploadPdf', controller.manage.standardLibrary.uploadPdf);
  router.post('/manage/standardLibrary/getStandardList', controller.manage.standardLibrary.getStandardList);
  router.post('/manage/standardLibrary/editStandardInfo', controller.manage.standardLibrary.editStandardInfo);
  router.post('/manage/standardLibrary/delStandardInfo', controller.manage.standardLibrary.delStandardInfo);
  router.post('/manage/standardLibrary/replaceFile', controller.manage.standardLibrary.replaceFile);

  // 大屏数据
  router.get('/manage/dataBigScreen/getScreenTopData', controller.manage.dataBigScreen.getScreenTopData);
  router.get('/manage/dataBigScreen/getScreenLeftData', controller.manage.dataBigScreen.getScreenLeftData);
  router.get('/manage/dataBigScreen/getScreenRightData', controller.manage.dataBigScreen.getScreenRightData);
  router.get(
    '/manage/dataBigScreen/getOnlineMonitorData',
    controller.manage.dataBigScreen.getOnlineMonitorData
  );

  // 权限
  router.get('/manage/policyManage/getScopeEnterpriseList', controller.manage.policyManage.getScopeEnterpriseList);
  router.get('/manage/policyManage/getScopeWorkplaceList', controller.manage.policyManage.getScopeWorkplaceList);
  router.get('/manage/policyManage/getWorkplaceList', controller.manage.policyManage.getWorkplaceList);
  router.get('/manage/policyManage/getAdminGroupList', controller.manage.policyManage.getAdminGroupList);
  // router.get('/manage/policyManage/getEmployeeInfo', controller.manage.policyManage.getEmployeeInfo);
  router.post('/manage/policyManage/createPolicy', controller.manage.policyManage.createPolicy);
  router.get('/manage/policyManage/getPolicyList', controller.manage.policyManage.getPolicyList);
  router.get('/manage/policyManage/getPolicyInfo', controller.manage.policyManage.getPolicyInfo);
  router.post('/manage/policyManage/updatePolicy', controller.manage.policyManage.updatePolicy);
  router.get('/manage/policyManage/getScopeDingtreeList', controller.manage.policyManage.getScopeDingtreeList);
  router.get('/manage/policyManage/getDingtreeAndUserList', controller.manage.policyManage.getDingtreeAndUserList);
  router.get('/manage/policyManage/getUserList', controller.manage.policyManage.getUserList);
  router.get('/manage/policyManage/getEnterpriseList', controller.manage.policyManage.getEnterpriseList);
  router.get('/manage/policyManage/getDingtreeList', controller.manage.policyManage.getDingtreeList);
  router.post('/manage/policyManage/addQyGroup', controller.manage.policyManage.addQyGroup);
  router.get('/manage/policyManage/getQyGroupList', controller.manage.policyManage.getQyGroupList);
  router.get('/manage/policyManage/deleteQyGroup', controller.manage.policyManage.deleteQyGroup);
  router.post('/manage/policyManage/updateOneQyGroup', controller.manage.policyManage.updateOneQyGroup);
  router.get('/manage/policyManage/getQyResourceList', controller.manage.policyManage.getQyResourceList);
  router.get('/manage/policyManage/getOneQyGroup', controller.manage.policyManage.getOneQyGroup);
  router.get('/manage/policyManage/getIsSuperAdmin', controller.manage.policyManage.getIsSuperAdmin);
  router.get('/manage/policyManage/getSuperAdminUser', controller.manage.policyManage.getSuperAdminUser);
  router.post('/manage/policyManage/deletePolicy', controller.manage.policyManage.deletePolicy);
  router.get('/manage/policyManage/getIsGroupAdmin', controller.manage.policyManage.getIsGroupAdmin);
  // 山西焦煤的统一认证登陆验证接口
  router.get('/api/admin/sxccSignIn', controller.api.admin.sxccSignIn);
  router.get('/api/ssoLogin', controller.api.admin.ssoLogin);
  // 获取是否
  router.get('/manage/adminResource/getProjectVisibilityEnabled', controller.manage.adminResource.getProjectVisibilityEnabled);
  router.get('/manage/adminResource/getRadioButtonEnabled', controller.manage.adminResource.getRadioButtonEnabled);

  // 测试
  router.get('/api/testDataPermissione', controller.api.admin.testDataPermission);

  // 个案卡解析路由
  router.post('/zywsTools/caseCardList', controller.zywsTools.tjCaseCard.getList); // 获取个案卡历史
  router.post('/zywsTools/reParseCard', controller.zywsTools.tjCaseCard.reParseCard); // 重新解析个案卡
  router.get('/zywsTools/caseCardRecord', controller.zywsTools.tjCaseCard.getCaseCard); // 获取个案卡
  router.post('/zywsTools/caseCardRecord', controller.zywsTools.tjCaseCard.uploadCaseCard); // 上传个案卡
  router.delete('/zywsTools/caseCardRecord', controller.zywsTools.tjCaseCard.delCaseCard); // 删除个案卡
  router.put('/zywsTools/caseCardRecord', controller.zywsTools.tjCaseCard.updateCaseCard); // 更新个案卡
  router.put('/zywsTools/dockingCaseCard', controller.zywsTools.tjCaseCard.dockingCaseCard); // 对接个案卡

  // // Todo API路由
  // router.post('/api/todo', controller.api.todo.create);
  // router.get('/api/todo', controller.api.todo.list);
  // router.get('/api/todo/:id', controller.api.todo.get);
  // router.put('/api/todo/:id', controller.api.todo.update);
  // router.delete('/api/todo/:id', controller.api.todo.delete);

  // 万华商城订单管理路由
  router.get('/manage/wh-orders', controller.manage.whOrderController.getOrderList);
  router.get('/manage/wh-orders/:id', controller.manage.whOrderController.getOrderDetail);
  router.post('/manage/wh-orders', controller.manage.whOrderController.createOrder);
  router.post('/manage/wh-orders/updateStatus', controller.manage.whOrderController.updateOrderStatus);
  router.post('/manage/wh-orders/delete', controller.manage.whOrderController.deleteOrder);

  // 万华商城商品管理路由
  router.get('/manage/wh-goods', controller.manage.whGoodsController.getGoodsList);
  router.get('/manage/wh-goods/detail', controller.manage.whGoodsController.getGoodsDetail);
  router.post('/manage/wh-goods/bind-inventory', controller.manage.whGoodsController.bindInventory);
  router.post('/manage/wh-goods/unbind-inventory', controller.manage.whGoodsController.unbindInventory);
  router.post('/manage/wh-goods/create-and-bind-inventory', controller.manage.whGoodsController.createAndBindInventory);

  // 万华商城库存调整记录管理
  router.get('/manage/wh-stock-records', controller.manage.whStockRecord.list);
  router.get('/manage/wh-stock-records/detail', controller.manage.whStockRecord.detail);
  router.post('/manage/wh-stock-records', controller.manage.whStockRecord.create);
  router.get('/manage/protective-supplies-categories', controller.manage.whGoodsController.getInventoryCategoryList);

  // 工伤管理系统API
  router.get('/manage/whIndustrialInjury/getList', controller.manage.whIndustrialInjury.getList);
  router.get('/manage/whIndustrialInjury/getDetail', controller.manage.whIndustrialInjury.getDetail);
  router.post('/manage/whIndustrialInjury/save', controller.manage.whIndustrialInjury.save);
  router.get('/manage/whIndustrialInjury/getStats', controller.manage.whIndustrialInjury.getStats);
  router.post('/manage/whIndustrialInjury/updateStatus', controller.manage.whIndustrialInjury.updateStatus);
  router.post('/manage/whIndustrialInjury/addFollowUp', controller.manage.whIndustrialInjury.addFollowUp);
  router.post('/manage/whIndustrialInjury/fetchFromPi', controller.manage.whIndustrialInjury.fetchFromPi);
  router.post('/manage/whIndustrialInjury/updateProcessingStatus', controller.manage.whIndustrialInjury.updateProcessingStatus);
  router.get('/manage/whIndustrialInjury/getDepartmentList', controller.manage.whIndustrialInjury.getDepartmentList);
  router.get('/manage/whIndustrialInjury/getModuleList', controller.manage.whIndustrialInjury.getModuleList);

  // 工作单元级联选择器API
  router.get('/api/workunit/cascade', controller.api.workUnitCascade.getCascadeData);
  router.get('/api/workunit/search', controller.api.workUnitCascade.globalSearch);
};
