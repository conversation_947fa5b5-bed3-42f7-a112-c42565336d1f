/**
 * @file 仓库管理服务
 * @description 提供防护用品仓库的增删改查和权限管理功能
 */
const Service = require('egg').Service;

class WarehouseService extends Service {
  /**
   * 获取用户有权限管理的仓库列表
   */
  async getWarehouseList() {
    const { ctx } = this;
    const userid = ctx.session.adminUserInfo._id;

    // 检查是否为超级管理员
    const isSuperAdmin = await ctx.helper.getScopeData('superAdmin', userid);

    console.log('仓库列表权限检查:', {
      userid,
      isSuperAdmin,
      EnterpriseID: ctx.session.adminUserInfo.EnterpriseID,
    });

    if (isSuperAdmin === '1') {
      // 超级管理员可以看到所有仓库
      console.log('超级管理员权限，返回所有仓库');
      return await this.getAllWarehouses();
    }

    // 获取用户的millConstruction权限
    const millConstructionIds = await ctx.helper.getScopeData('millConstruction_ids', userid);

    // 查询用户有权限的仓库
    const warehouses = await this.getUserAccessibleWarehouses(millConstructionIds);

    return warehouses;
  }

  /**
   * 获取所有仓库（超级管理员专用）
   */
  async getAllWarehouses() {
    const { ctx } = this;
    const EnterpriseID = ctx.session.adminUserInfo.EnterpriseID;

    console.log('超级管理员获取所有仓库:', { EnterpriseID });

    // 查询当前企业的所有仓库
    const warehouses = await ctx.model.Warehouse.find({
      EnterpriseID,
    }).sort({ isPublic: -1, createdAt: 1 });

    console.log('查询到的仓库数量:', warehouses.length);
    console.log('仓库列表:', warehouses.map(w => ({
      _id: w._id,
      name: w.name,
      isPublic: w.isPublic,
      managementScopeCount: w.managementScope ? w.managementScope.length : 0,
    })));

    return warehouses;
  }

  /**
   * 获取用户可访问的仓库
   */
  async getUserAccessibleWarehouses(millConstructionIds) {
    const { ctx } = this;
    const EnterpriseID = ctx.session.adminUserInfo.EnterpriseID;

    // 查询公共仓库
    const publicWarehouse = await ctx.model.Warehouse.findOne({
      isPublic: true,
      EnterpriseID,
    });

    // 如果用户没有millConstruction权限，只返回公共仓库
    if (!millConstructionIds || millConstructionIds.length === 0) {
      return publicWarehouse ? [ publicWarehouse ] : [];
    }

    // 构建查询条件
    const query = {
      EnterpriseID,
      $or: [
        { isPublic: true },
        { 'managementScope.fullId': { $in: millConstructionIds } },
      ],
    };

    // 查询用户有权限的仓库
    const warehouses = await ctx.model.Warehouse.find(query).sort({ isPublic: -1, createdAt: 1 });

    return warehouses;
  }

  /**
   * 创建仓库
   */
  async createWarehouse(data) {
    const { ctx } = this;
    const { name, managementScope } = data;
    const EnterpriseID = ctx.session.adminUserInfo.EnterpriseID;

    // 验证仓库名称是否重复
    const existingWarehouse = await ctx.model.Warehouse.findOne({
      name,
      EnterpriseID,
    });

    if (existingWarehouse) {
      throw new Error('已存在同名称的仓库');
    }

    // 处理管理范围冲突
    const filteredScope = await this.resolveManagementScopeConflicts(managementScope);

    // 创建仓库
    const warehouse = await ctx.model.Warehouse.create({
      name,
      managementScope: filteredScope,
      EnterpriseID,
      isPublic: false,
    });

    return warehouse;
  }

  /**
   * 更新仓库
   */
  async updateWarehouse(data) {
    const { ctx } = this;
    const { _id, name, managementScope } = data;
    const EnterpriseID = ctx.session.adminUserInfo.EnterpriseID;

    // 验证仓库名称是否重复
    const existingWarehouse = await ctx.model.Warehouse.findOne({
      name,
      EnterpriseID,
      _id: { $ne: _id },
    });

    if (existingWarehouse) {
      throw new Error('已存在同名称的仓库');
    }

    // 处理管理范围冲突
    const filteredScope = await this.resolveManagementScopeConflicts(managementScope, _id);

    // 更新仓库
    const warehouse = await ctx.model.Warehouse.findByIdAndUpdate(_id, {
      name,
      managementScope: filteredScope,
      updatedAt: new Date(),
    }, { new: true });

    return warehouse;
  }

  /**
   * 删除仓库
   */
  async deleteWarehouse(warehouseId) {
    const { ctx } = this;

    // 查询仓库
    const warehouse = await ctx.model.Warehouse.findById(warehouseId);

    if (!warehouse) {
      throw new Error('仓库不存在');
    }

    if (warehouse.isPublic) {
      throw new Error('公共仓库不能删除');
    }

    // 开启事务
    const session = await ctx.app.mongoose.startSession();
    session.startTransaction();

    try {
      // 删除仓库
      await ctx.model.Warehouse.findByIdAndDelete(warehouseId, { session });

      // 删除该仓库的所有数据
      await this.deleteWarehouseData(warehouseId, session);

      // 提交事务
      await session.commitTransaction();
      session.endSession();

      return { success: true, message: '删除成功' };
    } catch (error) {
      // 回滚事务
      await session.abortTransaction();
      session.endSession();
      throw error;
    }
  }

  /**
   * 删除仓库数据
   */
  async deleteWarehouseData(warehouseId, session) {
    const { ctx } = this;

    try {
      // 删除防护用品清单
      if (ctx.model.ProtectiveSuppliesList) {
        await ctx.model.ProtectiveSuppliesList.deleteMany({ warehouseId }, { session });
      }

      // 删除配发标准
      if (ctx.model.ProtectionPlan) {
        await ctx.model.ProtectionPlan.deleteMany({ warehouseId }, { session });
      }

      // 删除领用记录
      if (ctx.model.ReceiveRecord) {
        await ctx.model.ReceiveRecord.deleteMany({ warehouseId }, { session });
      }

      // 删除领用申请
      if (ctx.model.ApplicationRecord) {
        await ctx.model.ApplicationRecord.deleteMany({ warehouseId }, { session });
      }

      // 删除报废记录
      if (ctx.model.ScrapRecord) {
        await ctx.model.ScrapRecord.deleteMany({ warehouseId }, { session });
      }
    } catch (error) {
      ctx.logger.error('删除仓库数据失败:', error);
      throw error;
    }
  }

  /**
   * 解决管理范围冲突
   */
  async resolveManagementScopeConflicts(managementScope, currentWarehouseId = null) {
    const { ctx } = this;
    const EnterpriseID = ctx.session.adminUserInfo.EnterpriseID;

    // 查询其他独立仓库
    const query = {
      isPublic: false,
      EnterpriseID,
    };

    if (currentWarehouseId) {
      query._id = { $ne: currentWarehouseId };
    }

    const otherWarehouses = await ctx.model.Warehouse.find(query);

    // 收集已被管理的范围
    const managedFullIds = new Set();
    otherWarehouses.forEach(warehouse => {
      warehouse.managementScope.forEach(scope => {
        managedFullIds.add(scope.fullId);
      });
    });

    // 过滤掉冲突的管理范围
    return managementScope.filter(scope => !managedFullIds.has(scope.fullId));
  }
  /**
   * 根据企业ID获取工作场所（懒加载）
   * @param {string} enterpriseId - 企业ID
   * @param {string} currentWarehouseId - 当前编辑的仓库ID（排除冲突检测）
   */
  async getWorkplacesByCompany(enterpriseId, currentWarehouseId = null) {
    const { ctx } = this;
    // 使用聚合查询获取指定企业的工作场所数据（保持树形结构）
    const pipeline = [
      // 匹配企业
      {
        $match: {
          EnterpriseID: enterpriseId,
        },
      },
      // 只保留需要的字段
      {
        $project: {
          _id: 1,
          name: 1,
          EnterpriseID: 1,
          children: 1,
        },
      },
    ];

    // 执行聚合查询
    const result = await ctx.model.MillConstruction.aggregate(pipeline);

    if (!result || result.length === 0) {
      throw new Error('未找到指定的企业数据');
    }

    // 调试信息
    console.log('聚合查询结果:', {
      enterpriseId,
      resultCount: result.length,
      enterprises: result.map(ent => ({
        id: ent._id,
        name: ent.name,
        enterpriseId: ent.EnterpriseID,
        childrenCount: ent.children ? ent.children.length : 0,
      })),
    });

    // 查询已被其他仓库管理的范围（排除当前编辑的仓库）
    const managedFullIds = await this.getManagedFullIds(currentWarehouseId);

    // 处理所有企业记录，保持原来的层级结构
    console.log(`查询到 ${result.length} 个企业记录`);

    // 直接处理result，但在transformWorkplacesToTree中使用正确的millId
    const workplaces = result.map(enterprise => {
      const millId = enterprise._id; // 使用MillConstruction的_id作为millId

      console.log(`处理企业: ${enterprise.name}, millId: ${millId}`);
      console.log(`企业下工作场所数量: ${enterprise.children ? enterprise.children.length : 0}`);

      // 保持企业层级，但在内部使用millId构建fullId
      return {
        ...enterprise,
        children: this.transformWorkplacesToTree(enterprise.children || [], managedFullIds, millId),
      };
    });

    console.log('转换后的工作场所数据:', {
      workplacesCount: workplaces.length,
      workplaces: workplaces.map(w => ({
        fullId: w.fullId,
        label: w.label,
        disabled: w.disabled,
        childrenCount: w.children ? w.children.length : 0,
      })),
    });

    return workplaces;
  }

  /**
   * 获取公司列表（基于用户权限）
   */
  async getCompanyList(currentWarehouseId = null) {
    const { ctx } = this;

    // 获取用户的millConstruction权限
    const enterpriseIds = await ctx.helper.getScopeData('enterprise_ids');

    // 使用聚合查询获取公司列表
    const pipeline = [
      {
        $match: {
          EnterpriseID: { $in: enterpriseIds },
        },
      },
      {
        $group: {
          _id: '$EnterpriseID',
        },
      },
      {
        $lookup: {
          from: 'adminorgs',
          localField: '_id',
          foreignField: '_id',
          as: 'enterprise',
        },
      },
      {
        $lookup: {
          from: 'flatMillConstructionMaterialized',
          let: { enterpriseId: '$_id' },
          pipeline: [
            {
              $match: {
                $expr: { $eq: [ '$EnterpriseID', '$$enterpriseId' ] },
                state: '1',
              },
            },
            {
              $group: {
                _id: null,
                totalWorkplaces: { $sum: 1 },
              },
            },
          ],
          as: 'workplaceStats',
        },
      },
      {
        $project: {
          _id: 1,
          label: {
            $ifNull: [
              { $arrayElemAt: [ '$enterprise.shortName', 0 ] },
              { $arrayElemAt: [ '$enterprise.cname', 0 ] },
            ],
          },
          fullId: '$_id',
          totalWorkplaces: {
            $ifNull: [
              { $arrayElemAt: [ '$workplaceStats.totalWorkplaces', 0 ] },
              0,
            ],
          },
        },
      },
      {
        $sort: {
          _id: 1,
        },
      },
    ];

    const companies = await ctx.service.db.aggregate('MillConstruction', pipeline);

    // 获取已被管理的范围（排除当前编辑的仓库）
    const managedFullIds = await this.getManagedFullIds(currentWarehouseId);

    // 标记已占用状态
    const result = companies.map(company => ({
      ...company,
      value: company._id,
      disabled: managedFullIds.has(company._id), // 标记是否已被占用
    }));

    console.log('公司列表查询结果（含占用状态）:', result);

    return result;
  }

  /**
   * 获取车间岗位树（保留兼容性）
   */
  async getMillConstructionTree(currentWarehouseId = null, EnterpriseID = null) {
    const { ctx } = this;
    const userid = ctx.session.adminUserInfo._id;
    const targetEnterpriseID = EnterpriseID || ctx.session.adminUserInfo.EnterpriseID;

    // 检查是否为超级管理员
    const isSuperAdmin = await ctx.helper.getScopeData('superAdmin', userid);

    let millConstructions;

    if (isSuperAdmin === '1') {
      // 超级管理员可以看到所有车间岗位数据
      millConstructions = await ctx.service.db.find(
        'MillConstruction',
        { EnterpriseID: targetEnterpriseID },
        {},
        { sort: { sortIndex: 1 } }
      );
    } else {
      // 获取用户的millConstruction权限
      const millConstructionIds = await ctx.helper.getScopeData('millConstruction_ids', userid);

      // 如果用户没有权限，返回空数组
      if (!millConstructionIds || millConstructionIds.length === 0) {
        return [];
      }

      // 查询用户有权限的车间岗位数据
      millConstructions = await ctx.service.db.find(
        'MillConstruction',
        {
          EnterpriseID: targetEnterpriseID,
          _id: { $in: millConstructionIds },
        },
        {},
        { sort: { sortIndex: 1 } }
      );
    }

    // 查询已被其他仓库管理的范围（排除当前编辑的仓库）
    const managedFullIds = await this.getManagedFullIds(currentWarehouseId);

    // 转换为树形结构并标记已被管理的节点
    const tree = this.transformToTree(millConstructions, managedFullIds);

    return tree;
  }

  /**
   * 获取已被管理的范围
   */
  async getManagedFullIds(excludeWarehouseId = null) {
    const { ctx } = this;
    const EnterpriseID = ctx.session.adminUserInfo.EnterpriseID;

    // 查询独立仓库（排除指定的仓库）
    const query = {
      isPublic: false,
      EnterpriseID,
    };

    if (excludeWarehouseId) {
      query._id = { $ne: excludeWarehouseId };
    }

    const warehouses = await ctx.model.Warehouse.find(query);

    // 收集已被管理的范围
    const managedFullIds = new Set();
    warehouses.forEach(warehouse => {
      warehouse.managementScope.forEach(scope => {
        managedFullIds.add(scope.fullId);
      });
    });

    return managedFullIds;
  }

  /**
   * 转换工作场所为树形结构
   */
  transformWorkplacesToTree(workspaces, managedFullIds, parentId) {
    if (!workspaces || !Array.isArray(workspaces)) {
      return [];
    }

    return workspaces.map(workspace => {
      // 构建fullId
      const fullId = `${parentId}_child_workspaces_${workspace._id}`;

      // 检查是否被其他仓库管理
      const isDisabled = managedFullIds.has(fullId);

      // 构建节点
      const node = {
        fullId,
        label: workspace.name,
        level: 'workspaces',
        disabled: isDisabled,
      };

      // 处理子节点（岗位）
      const stations = workspace.children || [];
      if (stations && Array.isArray(stations)) {
        node.children = stations.map(station => {
          // 构建岗位的fullId
          const stationFullId = `${fullId}_child_stations_${station._id}`;

          // 检查岗位是否被其他仓库管理
          const isStationDisabled = managedFullIds.has(stationFullId);

          // 返回岗位节点
          return {
            fullId: stationFullId,
            label: station.name || `岗位_${station._id}`, // 如果没有名称，使用ID作为标签
            level: 'stations',
            disabled: isStationDisabled,
          };
        }); // 不过滤，返回所有岗位
      } else {
        node.children = []; // 确保有children属性
      }

      return node;
    }); // 不过滤，返回所有车间
  }

  /**
   * 转换为树形结构（保留兼容性）
   */
  transformToTree(millConstructions, managedFullIds) {
    const tree = [];

    millConstructions.forEach(item => {
      if (item.category === 'mill') {
        // 处理厂房层级：mill → workspaces → stations
        const millNode = {
          id: item._id,
          fullId: item._id,
          label: `🏭 ${item.name}`, // 添加工厂图标
          level: 'mill',
          category: item.category,
          disabled: managedFullIds.has(item._id),
          children: [],
        };

        if (item.children && item.children.length > 0) {
          item.children.forEach(workspace => {
            const workspaceFullId = `${item._id}_child_workspaces_${workspace._id}`;
            const workspaceNode = {
              id: workspace._id,
              fullId: workspaceFullId,
              label: `🏢 ${workspace.name}`, // 添加车间图标
              level: 'workspaces',
              category: workspace.category,
              disabled: managedFullIds.has(workspaceFullId),
              children: [],
            };

            if (workspace.children && workspace.children.length > 0) {
              workspace.children.forEach(station => {
                const stationFullId = `${item._id}_child_workspaces_${workspace._id}_child_stations_${station._id}`;
                const stationNode = {
                  id: station._id,
                  fullId: stationFullId,
                  label: `👷 ${station.name}`, // 添加岗位图标
                  level: 'stations',
                  category: station.category,
                  disabled: managedFullIds.has(stationFullId),
                };

                workspaceNode.children.push(stationNode);
              });
            }

            millNode.children.push(workspaceNode);
          });
        }

        tree.push(millNode);
      } else if (item.category === 'workspaces') {
        // 处理车间层级：workspaces → stations
        const workspaceNode = {
          id: item._id,
          fullId: item._id,
          label: `🏢 ${item.name}`, // 添加车间图标
          level: 'workspaces',
          category: item.category,
          disabled: managedFullIds.has(item._id),
          children: [],
        };

        if (item.children && item.children.length > 0) {
          item.children.forEach(station => {
            const stationFullId = `${item._id}_child_workspaces_${station._id}`;
            const stationNode = {
              id: station._id,
              fullId: stationFullId,
              label: `👷 ${station.name}`, // 添加岗位图标
              level: 'stations',
              category: station.category,
              disabled: managedFullIds.has(stationFullId),
            };

            workspaceNode.children.push(stationNode);
          });
        }

        tree.push(workspaceNode);
      }
    });

    return tree;
  }

  /**
   * 初始化公共仓库
   */
  async initPublicWarehouse() {
    const { ctx } = this;
    const EnterpriseID = ctx.session.adminUserInfo.EnterpriseID;

    // 检查是否已存在公共仓库
    const existingPublicWarehouse = await ctx.model.Warehouse.findOne({
      isPublic: true,
      EnterpriseID,
    });

    if (!existingPublicWarehouse) {
      // 创建公共仓库
      await ctx.model.Warehouse.create({
        name: '公共仓库',
        EnterpriseID,
        isPublic: true,
        managementScope: [],
      });
    }
  }
}

module.exports = WarehouseService;
